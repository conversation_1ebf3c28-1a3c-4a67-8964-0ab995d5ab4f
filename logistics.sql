/*
 Navicat Premium Data Transfer

 Source Server         : 127.0.0.1_3306
 Source Server Type    : MySQL
 Source Server Version : 80040
 Source Host           : 127.0.0.1:3306
 Source Schema         : logistics

 Target Server Type    : MySQL
 Target Server Version : 80040
 File Encoding         : 65001

 Date: 16/05/2025 14:16:09
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin
-- ----------------------------
DROP TABLE IF EXISTS `admin`;
CREATE TABLE `admin`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `roles` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `create_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of admin
-- ----------------------------
INSERT INTO `admin` VALUES ('a19dc35b-3276-445b-9a07-edf4778e24a5', '<EMAIL>', '123456', 'ROLE_ADMIN;ROLE_COMMODITY;ROLE_SALE;ROLE_EMPLOYEE;ROLE_WAREHOUSE', '2025-05-16 14:14:51');
INSERT INTO `admin` VALUES ('e236bf35-e64b-4026-9685-6d7014fd4cc0', '<EMAIL>', '123456', 'ROLE_SUPER_ADMIN', '2025-05-16 14:14:44');

-- ----------------------------
-- Table structure for code
-- ----------------------------
DROP TABLE IF EXISTS `code`;
CREATE TABLE `code`  (
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `exp` bigint(0) NULL DEFAULT NULL,
  PRIMARY KEY (`email`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of code
-- ----------------------------

-- ----------------------------
-- Table structure for commodity
-- ----------------------------
DROP TABLE IF EXISTS `commodity`;
CREATE TABLE `commodity`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `price` double NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `count` int(0) NULL DEFAULT 0,
  `create_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `update_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of commodity
-- ----------------------------
INSERT INTO `commodity` VALUES ('74cfed49-1442-4c14-89ba-c331b3d33fb8', '水果牙刷', 9.99, '卖肉的', 0, '2025-05-16 13:45:36', NULL);
INSERT INTO `commodity` VALUES ('e5f6g7h8-i9j0-k1l2-m3n4-o5p6q7r8s9t0', '智能手机', 4999.99, '最新款高性能智能手机', 150, '2024-01-20 09:00:00', '2024-05-10 14:30:00');
INSERT INTO `commodity` VALUES ('f6g7h8i9-j0k1-l2m3-n4o5-p6q7r8s9t0u1', '笔记本电脑', 6999.99, '轻薄高性能商务笔记本', 80, '2024-02-15 11:30:00', '2024-05-12 15:45:00');
INSERT INTO `commodity` VALUES ('g7h8i9j0-k1l2-m3n4-o5p6-q7r8s9t0u1v2', '智能手表', 1299.99, '多功能健康监测智能手表', 200, '2024-03-10 13:45:00', '2024-05-15 10:20:00');
INSERT INTO `commodity` VALUES ('h8i9j0k1-l2m3-n4o5-p6q7-r8s9t0u1v2w3', '无线耳机', 899.99, '降噪立体声无线耳机', 300, '2024-04-05 16:20:00', '2024-05-18 09:15:00');
INSERT INTO `commodity` VALUES ('i9j0k1l2-m3n4-o5p6-q7r8-s9t0u1v2w3x4', '平板电脑', 3499.99, '高清大屏娱乐平板', 120, '2024-05-01 10:10:00', '2025-01-05 11:30:00');

-- ----------------------------
-- Table structure for company
-- ----------------------------
DROP TABLE IF EXISTS `company`;
CREATE TABLE `company`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of company
-- ----------------------------

-- ----------------------------
-- Table structure for distribution
-- ----------------------------
DROP TABLE IF EXISTS `distribution`;
CREATE TABLE `distribution`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `did` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `vid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `driver` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `urgent` tinyint(1) NULL DEFAULT 0,
  `care` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `status` int(0) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of distribution
-- ----------------------------

-- ----------------------------
-- Table structure for driver
-- ----------------------------
DROP TABLE IF EXISTS `driver`;
CREATE TABLE `driver`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `id_card` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `license` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `score` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `driving` tinyint(1) NULL DEFAULT 0,
  `create_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `update_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of driver
-- ----------------------------

-- ----------------------------
-- Table structure for employee
-- ----------------------------
DROP TABLE IF EXISTS `employee`;
CREATE TABLE `employee`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `id_card` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `create_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `update_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of employee
-- ----------------------------

-- ----------------------------
-- Table structure for inventory
-- ----------------------------
DROP TABLE IF EXISTS `inventory`;
CREATE TABLE `inventory`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `wid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `cid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `count` int(0) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of inventory
-- ----------------------------
INSERT INTO `inventory` VALUES ('m3n4o5p6-q7r8-s9t0-u1v2-w3x4y5z6a7b8', 'j0k1l2m3-n4o5-p6q7-r8s9-t0u1v2w3x4y5', 'e5f6g7h8-i9j0-k1l2-m3n4-o5p6q7r8s9t0', '智能手机', 'A区-01-01', 100);
INSERT INTO `inventory` VALUES ('n4o5p6q7-r8s9-t0u1-v2w3-x4y5z6a7b8c9', 'j0k1l2m3-n4o5-p6q7-r8s9-t0u1v2w3x4y5', 'f6g7h8i9-j0k1-l2m3-n4o5-p6q7r8s9t0u1', '笔记本电脑', 'A区-02-01', 50);
INSERT INTO `inventory` VALUES ('o5p6q7r8-s9t0-u1v2-w3x4-y5z6a7b8c9d0', 'k1l2m3n4-o5p6-q7r8-s9t0-u1v2w3x4y5z6', 'e5f6g7h8-i9j0-k1l2-m3n4-o5p6q7r8s9t0', '智能手机', 'B区-01-01', 50);
INSERT INTO `inventory` VALUES ('p6q7r8s9-t0u1-v2w3-x4y5-z6a7b8c9d0e1', 'k1l2m3n4-o5p6-q7r8-s9t0-u1v2w3x4y5z6', 'g7h8i9j0-k1l2-m3n4-o5p6-q7r8s9t0u1v2', '智能手表', 'B区-02-01', 120);
INSERT INTO `inventory` VALUES ('q7r8s9t0-u1v2-w3x4-y5z6-a7b8c9d0e1f2', 'l2m3n4o5-p6q7-r8s9-t0u1-v2w3x4y5z6a7', 'h8i9j0k1-l2m3-n4o5-p6q7-r8s9t0u1v2w3', '无线耳机', 'C区-01-01', 200);

-- ----------------------------
-- Table structure for inventory_record
-- ----------------------------
DROP TABLE IF EXISTS `inventory_record`;
CREATE TABLE `inventory_record`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `wid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `cid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `count` int(0) NULL DEFAULT NULL,
  `type` int(0) NULL DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `create_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of inventory_record
-- ----------------------------
INSERT INTO `inventory_record` VALUES ('r8s9t0u1-v2w3-x4y5-z6a7-b8c9d0e1f2g3', '智能手机', 'j0k1l2m3-n4o5-p6q7-r8s9-t0u1v2w3x4y5', 'e5f6g7h8-i9j0-k1l2-m3n4-o5p6q7r8s9t0', 100, 1, '初始入库', '2024-01-25 09:30:00');
INSERT INTO `inventory_record` VALUES ('s9t0u1v2-w3x4-y5z6-a7b8-c9d0e1f2g3h4', '笔记本电脑', 'j0k1l2m3-n4o5-p6q7-r8s9-t0u1v2w3x4y5', 'f6g7h8i9-j0k1-l2m3-n4o5-p6q7r8s9t0u1', 50, 1, '初始入库', '2024-02-20 10:45:00');
INSERT INTO `inventory_record` VALUES ('t0u1v2w3-x4y5-z6a7-b8c9-d0e1f2g3h4i5', '智能手机', 'k1l2m3n4-o5p6-q7r8-s9t0-u1v2w3x4y5z6', 'e5f6g7h8-i9j0-k1l2-m3n4-o5p6q7r8s9t0', 50, 1, '调货入库', '2024-03-15 14:20:00');
INSERT INTO `inventory_record` VALUES ('u1v2w3x4-y5z6-a7b8-c9d0-e1f2g3h4i5j6', '智能手表', 'k1l2m3n4-o5p6-q7r8-s9t0-u1v2w3x4y5z6', 'g7h8i9j0-k1l2-m3n4-o5p6-q7r8s9t0u1v2', 120, 1, '新品入库', '2024-04-10 11:30:00');
INSERT INTO `inventory_record` VALUES ('v2w3x4y5-z6a7-b8c9-d0e1-f2g3h4i5j6k7', '无线耳机', 'l2m3n4o5-p6q7-r8s9-t0u1-v2w3x4y5z6a7', 'h8i9j0k1-l2m3-n4o5-p6q7-r8s9t0u1v2w3', 200, 1, '批量入库', '2024-05-05 15:45:00');
INSERT INTO `inventory_record` VALUES ('w3x4y5z6-a7b8-c9d0-e1f2-g3h4i5j6k7l8', '智能手机', 'j0k1l2m3-n4o5-p6q7-r8s9-t0u1v2w3x4y5', 'e5f6g7h8-i9j0-k1l2-m3n4-o5p6q7r8s9t0', 10, -1, '销售出库', '2024-06-10 09:15:00');

-- ----------------------------
-- Table structure for sale
-- ----------------------------
DROP TABLE IF EXISTS `sale`;
CREATE TABLE `sale`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `commodity` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `count` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `price` double NULL DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `pay` tinyint(1) NULL DEFAULT 0,
  `create_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sale
-- ----------------------------

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `create_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `update_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES ('c3d4e5f6-g7h8-i9j0-k1l2-m3n4o5p6q7r8', 'user1', '$2a$10$xVKxH.xdQFmMxLCMX2DPXeYzrgGJGVkyFYdOgmKM5JeOXRZwUuRdW', '2024-01-10 08:15:00', '2024-03-15 16:30:00');
INSERT INTO `user` VALUES ('d4e5f6g7-h8i9-j0k1-l2m3-n4o5p6q7r8s9', 'user2', '$2a$10$xVKxH.xdQFmMxLCMX2DPXeYzrgGJGVkyFYdOgmKM5JeOXRZwUuRdW', '2024-02-05 10:45:00', '2024-04-20 11:20:00');

-- ----------------------------
-- Table structure for vehicle
-- ----------------------------
DROP TABLE IF EXISTS `vehicle`;
CREATE TABLE `vehicle`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `driving` tinyint(1) NULL DEFAULT 0,
  `create_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vehicle
-- ----------------------------

-- ----------------------------
-- Table structure for warehouse
-- ----------------------------
DROP TABLE IF EXISTS `warehouse`;
CREATE TABLE `warehouse`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `principle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `create_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of warehouse
-- ----------------------------
INSERT INTO `warehouse` VALUES ('j0k1l2m3-n4o5-p6q7-r8s9-t0u1v2w3x4y5', '北京中央仓库', '张经理', '2024-01-05 08:00:00');
INSERT INTO `warehouse` VALUES ('k1l2m3n4-o5p6-q7r8-s9t0-u1v2w3x4y5z6', '上海配送中心', '李主管', '2024-02-10 09:30:00');
INSERT INTO `warehouse` VALUES ('l2m3n4o5-p6q7-r8s9-t0u1-v2w3x4y5z6a7', '广州物流中心', '王经理', '2024-03-15 10:45:00');

SET FOREIGN_KEY_CHECKS = 1;
