<template>
  <div class="login-container">
    <div class="login-background"></div>
    <div class="login-card">
      <div class="login-header">
        <img class="login-logo" src="../assets/logo.svg" alt="系统Logo"/>
        <h1 class="login-title">系统初始化</h1>
      </div>
      <div class="login-subtitle">首次使用需要创建管理员账号</div>

      <a-input
          v-model="form.email"
          size="large"
          class="login-input"
          placeholder="请输入管理员邮箱">
        <a-icon slot="prefix" type="user" />
      </a-input>
      <a-input-password
          v-model="form.password"
          size="large"
          class="login-input"
          placeholder="请设置管理员密码">
        <a-icon slot="prefix" type="lock" />
      </a-input-password>

      <a-button :loading="submitLoading" class="login-button" type="primary" @click="submitLogin">
        初始化系统
      </a-button>

      <div class="login-footer">
        <p>物流管理系统 © 2021 湖南信息学院 - 梅珂帆</p>
        <p>安全稳定 · 专业可靠 · 高效便捷</p>
      </div>
    </div>
  </div>
</template>

<script>
import {Init, IsInit} from "../api/admin";

export default {

  data() {
    return {
      spinning: true,
      sendLoading: false,
      submitType: '2', //1账号登录 2邮箱登录
      submitLoading: false,
      form: {
        password: '',
        email: '',
      },
    }
  },

  mounted() {
    IsInit().then((res) => {
      if (res.data) this.$router.push('/login')
    })
  },

  methods: {

    submitLogin() {
      if (this.checkEmail()) {
        Init(this.form).then((res) => {
          if (res.status) this.$router.push('/login')
        })
      }
    },

    checkEmail() {
      const emailRegex = new RegExp('^\\w{3,}(\\.\\w+)*@[A-z0-9]+(\\.[A-z]{2,5}){1,2}$')
      if (!emailRegex.test(this.form.email)) {
        this.$message.error('请输入正确格式的邮箱');
        return false
      } else {
        return true
      }
    },

  }

}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://img.freepik.com/free-photo/warehouse-logistics-concept_23-2149039269.jpg');
  background-size: cover;
  background-position: center;
  filter: blur(8px);
  opacity: 0.2;
  z-index: 0;
}

.login-card {
  width: 420px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 40px;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.login-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.login-logo {
  width: 50px;
  height: 50px;
  margin-right: 15px;
}

.login-title {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.login-subtitle {
  text-align: center;
  color: #666;
  font-size: 16px;
  margin-bottom: 30px;
}

.login-input {
  margin-bottom: 20px;
  height: 45px;
  border-radius: 6px;
  transition: all 0.3s;
}

.login-input:hover {
  border-color: #4a7bff;
}

.login-input:focus {
  box-shadow: 0 0 0 2px rgba(74, 123, 255, 0.2);
}

.login-button {
  width: 100%;
  height: 45px;
  border-radius: 6px;
  font-size: 16px;
  letter-spacing: 2px;
  background: #4a7bff;
  border-color: #4a7bff;
  transition: all 0.3s;
  margin-top: 10px;
}

.login-button:hover {
  background: #3a6ae8;
  border-color: #3a6ae8;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(74, 123, 255, 0.3);
}

.login-footer {
  margin-top: 30px;
  text-align: center;
  color: #999;
  font-size: 13px;
  line-height: 1.6;
}

/* 覆盖 Ant Design 样式 - 这些警告可以忽略 */
/deep/ .ant-btn-primary {
  background: #4a7bff;
  border-color: #4a7bff;
}

/deep/ .ant-input-affix-wrapper .ant-input {
  font-size: 14px;
}
</style>