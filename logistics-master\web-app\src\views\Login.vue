<template>
  <div class="login-container">
    <div class="login-background"></div>
    <div class="login-card">
      <div class="login-header">
        <img class="login-logo" src="../assets/logo.svg" alt="系统Logo"/>
        <h1 class="login-title">物流管理系统</h1>
      </div>
      <div class="login-subtitle">专业高效的物流管理解决方案</div>

      <a-tabs @change="tabClick" default-active-key="1" :tabBarStyle="{ textAlign: 'center' }" class="login-tabs">
        <a-tab-pane key="1" tab="密码登录">
          <a-input
              v-model="form.email"
              size="large"
              class="login-input"
              placeholder="请输入邮箱账号">
            <a-icon slot="prefix" type="user" />
          </a-input>
          <a-input-password
              v-model="form.password"
              size="large"
              class="login-input"
              placeholder="请输入密码">
            <a-icon slot="prefix" type="lock" />
          </a-input-password>
        </a-tab-pane>
        <a-tab-pane key="2" tab="验证码登录" force-render>
          <a-input
              v-model="form.email"
              size="large"
              class="login-input"
              placeholder="请输入邮箱账号">
            <a-icon slot="prefix" type="mail" />
          </a-input>
          <div class="code-container">
            <a-input
                v-model="form.code"
                size="large"
                class="login-input code-input"
                placeholder="请输入验证码">
              <a-icon slot="prefix" type="safety-certificate" />
            </a-input>
            <a-button class="code-button" :loading="sendLoading" @click="sendEmail" type="primary">
              获取验证码
            </a-button>
          </div>
        </a-tab-pane>
      </a-tabs>

      <div class="login-options">
        <a-checkbox v-model="form.remember">自动登录</a-checkbox>
        <a-button type="link" class="forgot-password">
          忘记密码?
        </a-button>
      </div>

      <a-button :loading="submitLoading" class="login-button" type="primary" @click="submitLogin">
        登录系统
      </a-button>

      <div class="login-footer">
        <p>物流管理系统 © 2025 湖南信息学院 - 梅珂帆</p>
        <p>安全稳定 · 专业可靠 · 高效便捷</p>
      </div>
    </div>
  </div>
</template>

<script>
import {AdminLogin, AdminSendEmail} from "@/api/admin";
import {IsInit} from "../api/admin";

export default {

  data() {
    return {
      sendLoading: false,
      submitType: '1', //1账号登录 2邮箱登录
      submitLoading: false,
      form: {
        password: '',
        email: '',
        code: '',
        remember: false,
      },
    }
  },

  mounted() {
    IsInit().then((res) => {
      if (!res.data) this.$router.push('/init')
    })
  },

  methods: {

    sendEmail() {
      if (this.checkEmail()) {
        this.sendLoading = true
        AdminSendEmail(this.form.email).then((res) => {
          if (res.status) this.$message.success("验证码发送成功")
          this.sendLoading = false
        })
      }
    },

    submitLogin() {
      if (this.checkEmail()) {
        let type = this.submitType === '1' ? "passwrod" : "email"
        AdminLogin(type, this.form).then((res) => {
          console.log(res.data)
          if (res.status) {
            this.$store.commit('user/saveToken', res.data.token)
            this.$store.commit('user/saveLoginUser', res.data.admin)
            setTimeout(() => {
              this.$router.push("/commodity")
              this.submitLoading = false
            }, 700)
            this.$message.success("登陆成功")
          }
        })
      }
    },

    tabClick(key) {
      this.submitType = key
    },

    checkEmail() {
      const emailRegex = new RegExp('^\\w{3,}(\\.\\w+)*@[A-z0-9]+(\\.[A-z]{2,5}){1,2}$')
      if (!emailRegex.test(this.form.email)) {
        this.$message.error('请输入正确格式的邮箱');
        return false
      } else {
        return true
      }
    },

  }

}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://img.freepik.com/free-photo/warehouse-logistics-concept_23-2149039269.jpg');
  background-size: cover;
  background-position: center;
  filter: blur(8px);
  opacity: 0.2;
  z-index: 0;
}

.login-card {
  width: 420px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 40px;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.login-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.login-logo {
  width: 50px;
  height: 50px;
  margin-right: 15px;
}

.login-title {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.login-subtitle {
  text-align: center;
  color: #666;
  font-size: 16px;
  margin-bottom: 30px;
}

.login-tabs {
  margin-bottom: 20px;
}

.login-input {
  margin-bottom: 20px;
  height: 45px;
  border-radius: 6px;
  transition: all 0.3s;
}

.login-input:hover {
  border-color: #4a7bff;
}

.login-input:focus {
  box-shadow: 0 0 0 2px rgba(74, 123, 255, 0.2);
}

.code-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.code-input {
  flex: 1;
}

.code-button {
  height: 45px;
  border-radius: 6px;
  width: 120px;
  background: #4a7bff;
  border-color: #4a7bff;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.forgot-password {
  color: #4a7bff;
  padding: 0;
}

.login-button {
  width: 100%;
  height: 45px;
  border-radius: 6px;
  font-size: 16px;
  letter-spacing: 2px;
  background: #4a7bff;
  border-color: #4a7bff;
  transition: all 0.3s;
}

.login-button:hover {
  background: #3a6ae8;
  border-color: #3a6ae8;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(74, 123, 255, 0.3);
}

.login-footer {
  margin-top: 30px;
  text-align: center;
  color: #999;
  font-size: 13px;
  line-height: 1.6;
}

/* 覆盖 Ant Design 样式 - 这些警告可以忽略 */
/deep/ .ant-tabs-bar {
  border-bottom: none !important;
  margin-bottom: 20px;
}

/deep/ .ant-tabs-nav {
  width: 100%;
}

/deep/ .ant-tabs-tab {
  font-size: 16px;
  padding: 12px 0;
}

/deep/ .ant-tabs-ink-bar {
  background-color: #4a7bff;
  height: 3px;
}

/deep/ .ant-btn-primary {
  background: #4a7bff;
  border-color: #4a7bff;
}

/deep/ .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #4a7bff;
  border-color: #4a7bff;
}

/deep/ .ant-input-affix-wrapper .ant-input {
  font-size: 14px;
}
</style>